name: Deploy to VPS

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy via SSH
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          password: ${{ secrets.VPS_PASSWORD }}
          port: 22
          script: |
            echo "📁 Moving to project folder"
            cd /***/fe/Planbook-FE || exit 1

            echo "🔄 Resetting and pulling latest code"
            git reset --hard
            git pull origin master || git pull origin main

            echo "🏗️ Building Docker image (without affecting running container)..."
            if ! docker compose -f docker-compose.yml build; then
              echo "❌ Build failed. Container still running. Abort."
              exit 1
            fi

            echo "🧼 Build succeeded. Stopping old container..."
            docker stop planbook-app || true
            docker rm planbook-app || true

            echo "🚀 Starting new container..."
            docker compose -f docker-compose.yml up -d

            echo "⏳ Waiting for container to initialize..."
            sleep 20

            echo "🩺 Performing health check..."
            if curl -fs http://localhost:3000/health || curl -fs http://localhost:3000; then
              echo "✅ Deployment successful!"
              docker image prune -f
            else
              echo "❌ Health check failed. Rolling back..."
              docker stop planbook-app || true
              docker rm planbook-app || true
              exit 1
            fi
