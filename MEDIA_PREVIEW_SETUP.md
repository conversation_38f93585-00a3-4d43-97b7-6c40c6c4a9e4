# Media Preview Components Setup

## 📦 Required Dependencies

### 1. Audio Preview (WaveSurfer.js)
```bash
npm install wavesurfer.js
# or
yarn add wavesurfer.js
```

### 2. Video Preview Options (Choose one)

#### Option A: React Player (Recommended - supports multiple formats)
```bash
npm install react-player
```

#### Option B: Video.js (Professional video player)
```bash
npm install video.js @videojs/themes
```

#### Option C: Plyr (Lightweight)
```bash
npm install plyr-react
```

## 🎯 Components Created

### 1. ImagePreviewGrid
- **Path**: `src/components/molecules/image-preview-grid/index.tsx`
- **Features**: 
  - Instagram-style grid layout (3 columns)
  - Hover effects with overlay icons
  - Modal for full-size view
  - Image info display (size, upload date)
  - Download functionality

### 2. AudioPreviewPlayer
- **Path**: `src/components/molecules/audio-preview-player/index.tsx`
- **Features**:
  - WaveSurfer.js integration for waveform visualization
  - Play/pause controls
  - Volume control with mute
  - Multiple audio file support
  - File info display

### 3. VideoPreviewPlayer
- **Path**: `src/components/molecules/video-preview-player/index.tsx`
- **Features**:
  - Grid layout for multiple videos
  - Play/pause controls
  - Fullscreen support
  - Volume control
  - Thumbnail support
  - Duration display

### 4. MediaPreviewTabs
- **Path**: `src/components/organisms/media-preview-tabs/index.tsx`
- **Features**:
  - Tabbed interface for different media types
  - Auto-grouping by media type
  - Count badges for each tab
  - Empty state handling

## 🔧 Usage Example

```tsx
import { MediaPreviewTabs } from "@/components/organisms/media-preview-tabs";

const mediaItems = [
  {
    id: "1",
    url: "https://example.com/image.jpg",
    name: "Sample Image",
    type: "image",
    size: 1024000,
    uploadedAt: "2024-01-01",
  },
  {
    id: "2", 
    url: "https://example.com/audio.mp3",
    name: "Sample Audio",
    type: "audio",
    duration: 180,
    size: 5120000,
    uploadedAt: "2024-01-01",
  },
  {
    id: "3",
    url: "https://example.com/video.mp4", 
    name: "Sample Video",
    type: "video",
    duration: 300,
    size: 10240000,
    thumbnail: "https://example.com/thumb.jpg",
    uploadedAt: "2024-01-01",
  }
];

function MyComponent() {
  return (
    <MediaPreviewTabs 
      mediaItems={mediaItems}
      defaultTab="image"
      className="max-w-6xl mx-auto"
    />
  );
}
```

## 🎨 Styling Features

### Image Grid
- **Layout**: CSS Grid with 3 columns
- **Aspect Ratio**: Square (1:1) for Instagram-like appearance
- **Hover Effects**: Scale transform + overlay with icons
- **Modal**: Full-screen overlay with image details

### Audio Player
- **Waveform**: Interactive waveform visualization
- **Controls**: Play/pause, volume, mute
- **Design**: Clean card-based layout
- **Responsive**: Adapts to container width

### Video Player
- **Layout**: Responsive grid (1/2/3 columns based on screen size)
- **Controls**: Overlay controls on hover
- **Aspect Ratio**: 16:9 video aspect ratio
- **Fullscreen**: Native fullscreen API support

## 🔄 Integration with Material Content

To integrate with your existing material content:

```tsx
// In your material-content component
import { MediaPreviewTabs } from "@/components/organisms/media-preview-tabs";

function MaterialContent() {
  const { data: materials } = useMaterialSearchService(activeTabId);
  
  // Transform API data to MediaItem format
  const mediaItems = materials?.data?.content?.map(item => ({
    id: item.id,
    url: item.url,
    name: item.name,
    type: getMediaType(item.mimeType), // Helper function
    mimeType: item.mimeType,
    size: item.size,
    uploadedAt: item.createdAt,
  })) || [];

  return (
    <div>
      {/* Your existing tabs */}
      <TabsComponent />
      
      {/* New media preview */}
      <MediaPreviewTabs mediaItems={mediaItems} />
    </div>
  );
}
```

## 📱 Responsive Design

All components are fully responsive:
- **Mobile**: Single column layout
- **Tablet**: 2 columns for videos, maintained grid for images
- **Desktop**: Full grid layouts

## 🎵 Audio Libraries Comparison

| Library | Pros | Cons | Best For |
|---------|------|------|----------|
| **WaveSurfer.js** | Beautiful waveforms, lightweight | Audio only | Audio visualization |
| **Howler.js** | Great audio control, cross-browser | No waveforms | Audio playback |
| **Tone.js** | Advanced audio features | Complex setup | Music applications |

## 🎬 Video Libraries Comparison

| Library | Pros | Cons | Best For |
|---------|------|------|----------|
| **React Player** | Supports many formats, easy setup | Limited customization | General video playback |
| **Video.js** | Highly customizable, professional | Larger bundle size | Professional video apps |
| **Plyr** | Lightweight, good design | Basic features | Simple video needs |

## 🚀 Next Steps

1. **Install dependencies** based on your choice
2. **Update WaveSurfer integration** in AudioPreviewPlayer
3. **Choose and integrate video library** in VideoPreviewPlayer  
4. **Test with real media files**
5. **Customize styling** to match your design system
