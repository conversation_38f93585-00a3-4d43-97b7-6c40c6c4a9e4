import ChatScreenTemplate from "@/components/templates/chat-screen";

export default function Page() {
  const messages = [
    {
      from: "user",
      content:
        "Dựa theo tài liệu hãy giúp tôi tạo ra đề cương giữa kì II theo chuẩn của B<PERSON> G<PERSON> D<PERSON>",
    },
    {
      from: "bot",
      content:
        "Vân<PERSON>, tôi sẽ đọc tài liệu để soạn ra đề cương cho bạn, vui lòng chờ trong ít phút...",
    },
  ];

  const tags = [
    "Giáo án",
    "Kế hoạch giảng dạy",
    "Slide bài giảng",
    "Đề cương thi",
  ];

  const results = [
    {
      name: "Đ<PERSON> cương thi giữa kì II",
      type: "DOC",
    },
    {
      name: "<PERSON><PERSON> cương hoá học",
      type: "PDF",
    },
  ];

  const documents = [
    {
      type: "PDF",
      name: "Chương 3: <PERSON><PERSON> hữu cơ",
      description: "<PERSON><PERSON><PERSON><PERSON> cứu các yếu tố ảnh hưởng...",
    },
    {
      type: "DOC",
      name: "Chương 4: <PERSON>",
      description: "<PERSON>ổ<PERSON> hợp kiến thức phản ứng hoá học...",
    },
  ];

  return (
    <div className="h-full">
      <ChatScreenTemplate
        messages={messages}
        tags={tags}
        results={results}
        documents={documents}
      />
    </div>
  );
}
