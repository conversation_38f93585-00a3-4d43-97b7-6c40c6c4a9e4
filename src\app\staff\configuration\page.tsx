"use client";
import CreateMaterialModal from "@/components/organisms/create-material-modal";
import { Button } from "@/components/ui/Button";
import { Plus } from "lucide-react";
import React, { useState } from "react";

function ConfigurationPage() {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  return (
    <div>
      <Button
        onClick={() => setIsCreateModalOpen(true)}
        className="flex items-center gap-2 float-end"
      >
        <Plus className="w-4 h-4" />
        Tạo hướng dẫn
      </Button>

      <CreateMaterialModal
        open={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateMaterial}
      />
    </div>
  );
}

export default ConfigurationPage;
