"use client";

import React, { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";
import { Play, Pause, Download, Volume2, VolumeX } from "lucide-react";
import { Button } from "@/components/ui/Button";

// Import WaveSurfer (you need to install: npm install wavesurfer.js)
// import WaveSurfer from "wavesurfer.js";

interface AudioItem {
  id: string;
  url: string;
  name: string;
  duration?: number;
  size?: number;
  uploadedAt?: string;
}

interface AudioPreviewPlayerProps {
  audioFiles: AudioItem[];
  className?: string;
}

export function AudioPreviewPlayer({ audioFiles, className }: AudioPreviewPlayerProps) {
  const [currentPlaying, setCurrentPlaying] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const waveformRefs = useRef<{ [key: string]: any }>({});
  const wavesurferInstances = useRef<{ [key: string]: any }>({});

  const formatDuration = (seconds?: number) => {
    if (!seconds) return "0:00";
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
  };

  // Initialize WaveSurfer for each audio file
  useEffect(() => {
    audioFiles.forEach((audio) => {
      if (waveformRefs.current[audio.id] && !wavesurferInstances.current[audio.id]) {
        // Uncomment when WaveSurfer is installed
        /*
        const wavesurfer = WaveSurfer.create({
          container: waveformRefs.current[audio.id],
          waveColor: '#3b82f6',
          progressColor: '#1d4ed8',
          cursorColor: '#1d4ed8',
          barWidth: 2,
          barRadius: 3,
          responsive: true,
          height: 60,
          normalize: true,
        });

        wavesurfer.load(audio.url);
        wavesurferInstances.current[audio.id] = wavesurfer;

        wavesurfer.on('ready', () => {
          console.log(`Audio ${audio.name} is ready`);
        });

        wavesurfer.on('play', () => {
          setCurrentPlaying(audio.id);
          setIsPlaying(true);
        });

        wavesurfer.on('pause', () => {
          setIsPlaying(false);
        });

        wavesurfer.on('finish', () => {
          setCurrentPlaying(null);
          setIsPlaying(false);
        });
        */
      }
    });

    return () => {
      // Cleanup WaveSurfer instances
      Object.values(wavesurferInstances.current).forEach((instance: any) => {
        if (instance && instance.destroy) {
          instance.destroy();
        }
      });
    };
  }, [audioFiles]);

  const handlePlayPause = (audioId: string) => {
    const wavesurfer = wavesurferInstances.current[audioId];
    
    if (currentPlaying && currentPlaying !== audioId) {
      // Stop other audio
      const currentWavesurfer = wavesurferInstances.current[currentPlaying];
      if (currentWavesurfer) {
        currentWavesurfer.pause();
      }
    }

    if (wavesurfer) {
      if (currentPlaying === audioId && isPlaying) {
        wavesurfer.pause();
      } else {
        wavesurfer.play();
      }
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    Object.values(wavesurferInstances.current).forEach((instance: any) => {
      if (instance && instance.setVolume) {
        instance.setVolume(newVolume);
      }
    });
  };

  const toggleMute = () => {
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    Object.values(wavesurferInstances.current).forEach((instance: any) => {
      if (instance && instance.setVolume) {
        instance.setVolume(newMuted ? 0 : volume);
      }
    });
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Volume Control */}
      <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
        <button onClick={toggleMute} className="text-gray-600 hover:text-gray-800">
          {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
        </button>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={isMuted ? 0 : volume}
          onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
          className="flex-1"
        />
        <span className="text-sm text-gray-600 w-8">{Math.round((isMuted ? 0 : volume) * 100)}%</span>
      </div>

      {/* Audio Files List */}
      {audioFiles.map((audio) => (
        <div key={audio.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
          {/* Audio Info */}
          <div className="flex items-center justify-between mb-3">
            <div>
              <h3 className="font-medium text-gray-900">{audio.name}</h3>
              <div className="text-sm text-gray-500">
                {audio.duration && <span>{formatDuration(audio.duration)}</span>}
                {audio.size && <span className="ml-2">• {formatFileSize(audio.size)}</span>}
                {audio.uploadedAt && (
                  <span className="ml-2">• {new Date(audio.uploadedAt).toLocaleDateString()}</span>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handlePlayPause(audio.id)}
                className="w-10 h-10 p-0"
              >
                {currentPlaying === audio.id && isPlaying ? (
                  <Pause className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
              </Button>
              <Button size="sm" variant="outline">
                <Download className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Waveform */}
          <div
            ref={(el) => {
              if (el) waveformRefs.current[audio.id] = el;
            }}
            className="w-full h-16 bg-gray-100 rounded cursor-pointer"
            onClick={() => handlePlayPause(audio.id)}
          >
            {/* Fallback when WaveSurfer is not loaded */}
            <div className="flex items-center justify-center h-full text-gray-500">
              <span className="text-sm">Click để phát âm thanh</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
