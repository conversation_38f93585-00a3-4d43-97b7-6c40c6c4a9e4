import React from "react";
import { KeywordForm } from "@/components/molecules/keyword-form";
import { HierarchicalDroppable } from "@/components/molecules/hierarchical-droppable";

interface HierarchicalContentProps {
  stepId: string;
  components: any[];
  formData: Record<string, string>;
  onFormDataChange: (id: string, value: string) => void;
  onDeleteComponent?: (id: string) => void;
  isEditMode?: boolean;
  level?: number;
  parentId?: string;
}

export function HierarchicalContent({
  stepId,
  components,
  formData,
  onFormDataChange,
  onDeleteComponent,
  isEditMode = false,
  level = 0,
  parentId,
}: HierarchicalContentProps) {
  console.log("🏗️ HierarchicalContent:", {
    stepId,
    parentId,
    level,
    componentsCount: components.length,
    components: components.map(c => ({ id: c.id, title: c.title, type: c.type }))
  });

  // Group components by their hierarchy level
  const groupedComponents = components.reduce((acc, component, index) => {
    // For now, treat all components at the same level
    // Later we can add logic to group by parentId or other hierarchy indicators
    acc.push({
      component,
      index,
      children: [] // TODO: Add logic to find child components
    });
    return acc;
  }, [] as any[]);

  return (
    <div className="space-y-4">
      {/* Drop zone before first component */}
      <HierarchicalDroppable
        stepId={stepId}
        parentId={parentId}
        level={level}
        index={0}
        className="min-h-[20px]"
      />

      {groupedComponents.map(({ component, index, children }, groupIndex) => (
        <div key={component.id} className="space-y-2">
          {/* Component content */}
          <div className="relative group">
            <KeywordForm
              keyword={component}
              value={formData[component?.id] || ""}
              onChange={(value) => onFormDataChange(component?.id, value)}
              index={index}
              isEditMode={isEditMode}
              onDelete={onDeleteComponent}
              level={level}
            />
          </div>

          {/* Drop zone for child components */}
          {(children.length > 0 || level < 2) && (
            <div className="ml-8 border-l-2 border-gray-200 pl-4">
              <HierarchicalDroppable
                stepId={stepId}
                parentId={component.id}
                level={level + 1}
                index={0}
                className="min-h-[40px] py-2"
              >
                {children.length > 0 && (
                  <HierarchicalContent
                    stepId={stepId}
                    components={children}
                    formData={formData}
                    onFormDataChange={onFormDataChange}
                    onDeleteComponent={onDeleteComponent}
                    isEditMode={isEditMode}
                    level={level + 1}
                    parentId={component.id}
                  />
                )}
              </HierarchicalDroppable>
            </div>
          )}

          {/* Drop zone after component */}
          <HierarchicalDroppable
            stepId={stepId}
            parentId={parentId}
            level={level}
            index={groupIndex + 1}
            className="min-h-[20px]"
          />
        </div>
      ))}

      {/* Drop zone at the end if no components */}
      {groupedComponents.length === 0 && (
        <HierarchicalDroppable
          stepId={stepId}
          parentId={parentId}
          level={level}
          index={0}
          className="min-h-[200px]"
        />
      )}
    </div>
  );
}
