import React from "react";
import { Droppable } from "@hello-pangea/dnd";
import { KeywordForm } from "@/components/molecules/keyword-form";
import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";

interface HierarchicalContentProps {
  stepId: string;
  components: any[];
  formData: Record<string, string>;
  onFormDataChange: (id: string, value: string) => void;
  onDeleteComponent?: (id: string) => void;
  isEditMode?: boolean;
}

export function HierarchicalContent({
  stepId,
  components,
  formData,
  onFormDataChange,
  onDeleteComponent,
  isEditMode = false,
}: HierarchicalContentProps) {
  console.log("🏗️ HierarchicalContent:", {
    stepId,
    componentsCount: components.length,
    components: components.map(c => ({ id: c.id, title: c.title, type: c.type }))
  });

  return (
    <div className="space-y-6">
      {/* Main drop zone for the step */}
      <Droppable droppableId={`form-${stepId}`}>
        {(provided, snapshot) => (
          <div
            ref={provided.innerRef}
            {...provided.droppableProps}
            className={cn(
              "min-h-[200px] transition-colors rounded-lg border-2 border-dashed",
              snapshot.isDraggingOver
                ? "border-blue-500 bg-blue-50"
                : "border-gray-300 bg-gray-50"
            )}
          >
            {components.length > 0 ? (
              <div className="p-4 space-y-6">
                {components.map((component, index) => (
                  <div key={component.id} className="space-y-4">
                    {/* Component itself */}
                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                      <KeywordForm
                        keyword={component}
                        value={formData[component?.id] || ""}
                        onChange={(value) => onFormDataChange(component?.id, value)}
                        index={index}
                        isEditMode={isEditMode}
                        onDelete={onDeleteComponent}
                      />
                    </div>

                    {/* Individual drop zone for this component */}
                    <Droppable droppableId={`form-${stepId}-after-${component.id}`}>
                      {(childProvided, childSnapshot) => (
                        <div
                          ref={childProvided.innerRef}
                          {...childProvided.droppableProps}
                          className={cn(
                            "min-h-[60px] transition-colors rounded border border-dashed mx-4",
                            childSnapshot.isDraggingOver
                              ? "border-green-500 bg-green-50"
                              : "border-gray-200 bg-gray-25"
                          )}
                        >
                          {childSnapshot.isDraggingOver ? (
                            <div className="flex items-center justify-center h-full text-green-600 py-4">
                              <Plus className="w-5 h-5 mr-2" />
                              <span className="text-sm font-medium">
                                Thả component sau "{component.title}"
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center justify-center h-full text-gray-400 py-4">
                              <span className="text-xs">
                                Kéo component vào đây để thêm sau "{component.title}"
                              </span>
                            </div>
                          )}
                          {childProvided.placeholder}
                        </div>
                      )}
                    </Droppable>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-center py-12">
                {snapshot.isDraggingOver ? (
                  <div className="text-blue-600">
                    <Plus className="w-16 h-16 mx-auto mb-4" />
                    <h3 className="text-lg font-calsans mb-2">
                      Thả component vào đây
                    </h3>
                    <p className="font-questrial">
                      Component sẽ được thêm vào step này
                    </p>
                  </div>
                ) : (
                  <div className="text-gray-400">
                    <Plus className="w-16 h-16 mx-auto mb-4" />
                    <h3 className="text-lg font-calsans text-gray-900 mb-2">
                      Chưa có nội dung
                    </h3>
                    <p className="text-gray-500 font-questrial">
                      Kéo component từ sidebar để thêm vào step này
                    </p>
                  </div>
                )}
              </div>
            )}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </div>
  );
}
