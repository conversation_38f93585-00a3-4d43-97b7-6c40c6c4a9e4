import React from "react";
import { Droppable } from "@hello-pangea/dnd";
import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";

interface HierarchicalDroppableProps {
  stepId: string;
  parentId?: string;
  level?: number;
  index?: number;
  children?: React.ReactNode;
  className?: string;
  showDropZone?: boolean;
}

export function HierarchicalDroppable({
  stepId,
  parentId,
  level = 0,
  index = 0,
  children,
  className,
  showDropZone = true,
}: HierarchicalDroppableProps) {
  // Create unique droppable ID based on hierarchy
  const droppableId = parentId 
    ? `form-${stepId}-parent-${parentId}-index-${index}`
    : `form-${stepId}-root`;

  console.log("🎯 HierarchicalDroppable:", {
    stepId,
    parentId,
    level,
    index,
    droppableId
  });

  if (!showDropZone) {
    return <div className={className}>{children}</div>;
  }

  return (
    <Droppable droppableId={droppableId}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.droppableProps}
          className={cn(
            "relative",
            snapshot.isDraggingOver && level === 0 && "bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg p-2",
            snapshot.isDraggingOver && level > 0 && "bg-green-50 border border-dashed border-green-300 rounded p-1",
            className
          )}
        >
          {/* Drop indicator for nested levels */}
          {snapshot.isDraggingOver && level > 0 && (
            <div className="absolute -top-2 left-0 right-0 flex items-center justify-center">
              <div className="bg-green-500 text-white text-xs px-2 py-1 rounded flex items-center gap-1">
                <Plus className="w-3 h-3" />
                Thả vào đây (Level {level})
              </div>
            </div>
          )}

          {children}

          {/* Empty state for root level */}
          {level === 0 && !children && (
            <div className="text-center py-12">
              {snapshot.isDraggingOver ? (
                <div className="text-blue-600">
                  <Plus className="w-16 h-16 mx-auto mb-4" />
                  <h3 className="text-lg font-calsans mb-2">
                    Thả component vào đây
                  </h3>
                  <p className="font-questrial">
                    Component sẽ được thêm vào vị trí này
                  </p>
                </div>
              ) : (
                <div className="text-gray-400">
                  <Plus className="w-16 h-16 mx-auto mb-4" />
                  <h3 className="text-lg font-calsans text-gray-900 mb-2">
                    Chưa có nội dung
                  </h3>
                  <p className="text-gray-500 font-questrial">
                    Kéo component từ sidebar để thêm vào đây
                  </p>
                </div>
              )}
            </div>
          )}

          {provided.placeholder}
        </div>
      )}
    </Droppable>
  );
}
