"use client";

import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { Eye, Download, Heart, Share2 } from "lucide-react";
import { Button } from "@/components/ui/Button";

interface ImageItem {
  id: string;
  url: string;
  name: string;
  size?: number;
  uploadedAt?: string;
}

interface ImagePreviewGridProps {
  images: ImageItem[];
  className?: string;
}

export function ImagePreviewGrid({ images, className }: ImagePreviewGridProps) {
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
  };

  const handleImageClick = (image: ImageItem) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  return (
    <>
      {/* Instagram-style Grid */}
      <div className={cn("grid grid-cols-3 gap-1", className)}>
        {images.map((image) => (
          <div
            key={image.id}
            className="relative aspect-square cursor-pointer group overflow-hidden rounded-lg"
            onClick={() => handleImageClick(image)}
          >
            <img
              src={image.url}
              alt={image.name}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
            />
            
            {/* Hover overlay */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
                <Eye className="w-6 h-6 text-white" />
                <Heart className="w-6 h-6 text-white" />
                <Share2 className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Modal for full view */}
      {selectedImage && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            {/* Close button */}
            <button
              onClick={closeModal}
              className="absolute -top-10 right-0 text-white hover:text-gray-300 text-2xl"
            >
              ✕
            </button>
            
            {/* Image */}
            <img
              src={selectedImage.url}
              alt={selectedImage.name}
              className="max-w-full max-h-[80vh] object-contain rounded-lg"
            />
            
            {/* Image info */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 text-white">
              <h3 className="text-lg font-semibold mb-2">{selectedImage.name}</h3>
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-300">
                  {selectedImage.size && <span>{formatFileSize(selectedImage.size)}</span>}
                  {selectedImage.uploadedAt && (
                    <span className="ml-2">• {new Date(selectedImage.uploadedAt).toLocaleDateString()}</span>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-white/20 border-white/30 text-white hover:bg-white/30"
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Tải xuống
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
