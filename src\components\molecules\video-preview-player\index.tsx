"use client";

import React, { useState, useRef } from "react";
import { cn } from "@/lib/utils";
import { Play, Pause, Volume2, VolumeX, Maximize, Download, MoreVertical } from "lucide-react";
import { Button } from "@/components/ui/Button";

// For advanced video features, consider these libraries:
// - react-player: npm install react-player (supports YouTube, Vimeo, etc.)
// - video.js: npm install video.js @videojs/themes (professional video player)
// - plyr: npm install plyr-react (lightweight, customizable)

interface VideoItem {
  id: string;
  url: string;
  name: string;
  duration?: number;
  size?: number;
  thumbnail?: string;
  uploadedAt?: string;
}

interface VideoPreviewPlayerProps {
  videos: VideoItem[];
  className?: string;
}

export function VideoPreviewPlayer({ videos, className }: VideoPreviewPlayerProps) {
  const [currentPlaying, setCurrentPlaying] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [fullscreenVideo, setFullscreenVideo] = useState<string | null>(null);
  const videoRefs = useRef<{ [key: string]: HTMLVideoElement }>({});

  const formatDuration = (seconds?: number) => {
    if (!seconds) return "0:00";
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i];
  };

  const handlePlayPause = (videoId: string) => {
    const video = videoRefs.current[videoId];
    if (!video) return;

    // Pause other videos
    if (currentPlaying && currentPlaying !== videoId) {
      const currentVideo = videoRefs.current[currentPlaying];
      if (currentVideo) {
        currentVideo.pause();
      }
    }

    if (currentPlaying === videoId && isPlaying) {
      video.pause();
      setIsPlaying(false);
    } else {
      video.play();
      setCurrentPlaying(videoId);
      setIsPlaying(true);
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    Object.values(videoRefs.current).forEach((video) => {
      if (video) {
        video.volume = newVolume;
      }
    });
  };

  const toggleMute = () => {
    const newMuted = !isMuted;
    setIsMuted(newMuted);
    Object.values(videoRefs.current).forEach((video) => {
      if (video) {
        video.muted = newMuted;
      }
    });
  };

  const handleFullscreen = (videoId: string) => {
    const video = videoRefs.current[videoId];
    if (video && video.requestFullscreen) {
      video.requestFullscreen();
      setFullscreenVideo(videoId);
    }
  };

  const handleVideoEnd = () => {
    setCurrentPlaying(null);
    setIsPlaying(false);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Global Controls */}
      <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-3">
          <button onClick={toggleMute} className="text-gray-600 hover:text-gray-800">
            {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
          </button>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={isMuted ? 0 : volume}
            onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
            className="w-24"
          />
          <span className="text-sm text-gray-600 w-8">{Math.round((isMuted ? 0 : volume) * 100)}%</span>
        </div>
      </div>

      {/* Video Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {videos.map((video) => (
          <div key={video.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
            {/* Video Player */}
            <div className="relative aspect-video bg-black group">
              <video
                ref={(el) => {
                  if (el) videoRefs.current[video.id] = el;
                }}
                src={video.url}
                poster={video.thumbnail}
                className="w-full h-full object-cover"
                onEnded={handleVideoEnd}
                onPlay={() => {
                  setCurrentPlaying(video.id);
                  setIsPlaying(true);
                }}
                onPause={() => setIsPlaying(false)}
              />
              
              {/* Video Controls Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-3">
                  <button
                    onClick={() => handlePlayPause(video.id)}
                    className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-3 text-white transition-all"
                  >
                    {currentPlaying === video.id && isPlaying ? (
                      <Pause className="w-6 h-6" />
                    ) : (
                      <Play className="w-6 h-6" />
                    )}
                  </button>
                  <button
                    onClick={() => handleFullscreen(video.id)}
                    className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-3 text-white transition-all"
                  >
                    <Maximize className="w-6 h-6" />
                  </button>
                </div>
              </div>

              {/* Duration Badge */}
              {video.duration && (
                <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                  {formatDuration(video.duration)}
                </div>
              )}
            </div>

            {/* Video Info */}
            <div className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 truncate">{video.name}</h3>
                  <div className="text-sm text-gray-500 mt-1">
                    {video.size && <span>{formatFileSize(video.size)}</span>}
                    {video.uploadedAt && (
                      <span className="ml-2">• {new Date(video.uploadedAt).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
                <div className="flex gap-1 ml-2">
                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                    <Download className="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
