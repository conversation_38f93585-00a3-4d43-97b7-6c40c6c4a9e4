import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { MoreVertical } from "lucide-react";
import { BookResponse } from "@/types";
import { getStatusVariant, translateStatus } from "@/utils/translateEnum";
import { Badge } from "@/components/ui/badge";

export const bookColumns: ColumnDef<BookResponse>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  // {
  //   accessorKey: "gradeId",
  //   header: "Khối",
  //   cell: ({ row }) => (
  //     <div className="flex items-center gap-2">
  //       <span className="font-medium ">{row.original.gradeId}</span>
  //     </div>
  //   ),
  // },
  {
    accessorKey: "grade",
    header: "Tên khối",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="font-medium">{row.original.subject?.grade?.name}</span>
      </div>
    ),
  },
  {
    accessorKey: "subject",
    header: "Môn học",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="font-medium">{row.original.subject?.name}</span>
      </div>
    ),
  },
  {
    accessorKey: "name",
    header: "Tên sách",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="font-medium">{row.original.name}</span>
      </div>
    ),
  },

  {
    accessorKey: "createdAt",
    header: "Ngày tạo",
    cell: ({ row }) => (
      <div className="text-muted-foreground truncate">
        {row.original.createdAt}
      </div>
    ),
  },
  {
    accessorKey: "updatedAt",
    header: "Ngày cập nhật",
    cell: ({ row }) => (
      <span className="text-muted-foreground">{row.original.updatedAt}</span>
    ),
  },
  {
    accessorKey: "status",
    header: "Trạng thái",
    cell: ({ row }) => {
      const status = row.original.status;
      const statusText = translateStatus(status || "");

      return <Badge variant={getStatusVariant(status)}>{statusText}</Badge>;
    },
  },
];
