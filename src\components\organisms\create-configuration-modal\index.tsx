"use client";

import React from "react";
import {
  <PERSON>alog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";

import { MaterialFormData } from "@/schemas/material.schema";
import CreateConfigurationForm from "../create-configuration-form";

interface CreateConfigurationModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit?: (data: MaterialFormData) => void;
}

export default function CreateConfigurationModal({
  open,
  onClose,
  onSubmit,
}: CreateConfigurationModalProps) {
  const handleSubmit = (data: MaterialFormData) => {
    console.log("Modal received data:", data);
    onSubmit?.(data);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            <PERSON><PERSON><PERSON> dẫn Mới
          </DialogTitle>
        </DialogHeader>

        <div className="mt-4">
          <CreateConfigurationForm onClose={onClose} onSubmit={handleSubmit} />
        </div>
      </DialogContent>
    </Dialog>
  );
}
