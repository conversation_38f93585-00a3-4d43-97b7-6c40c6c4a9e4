"use client";

import React, { useState, useMemo } from "react";
import { cn } from "@/lib/utils";
import { ImageIcon, Music, Video, FileText } from "lucide-react";
import { ImagePreviewGrid } from "@/components/molecules/image-preview-grid";
import { AudioPreviewPlayer } from "@/components/molecules/audio-preview-player";
import { VideoPreviewPlayer } from "@/components/molecules/video-preview-player";

interface MediaItem {
  id: string;
  url: string;
  name: string;
  type: "image" | "audio" | "video" | "document";
  mimeType?: string;
  duration?: number;
  size?: number;
  thumbnail?: string;
  uploadedAt?: string;
}

interface MediaPreviewTabsProps {
  mediaItems: MediaItem[];
  className?: string;
  defaultTab?: "image" | "audio" | "video" | "document";
}

export function MediaPreviewTabs({ 
  mediaItems, 
  className, 
  defaultTab = "image" 
}: MediaPreviewTabsProps) {
  const [activeTab, setActiveTab] = useState<"image" | "audio" | "video" | "document">(defaultTab);

  // Group media items by type
  const groupedMedia = useMemo(() => {
    return {
      image: mediaItems.filter(item => item.type === "image"),
      audio: mediaItems.filter(item => item.type === "audio"),
      video: mediaItems.filter(item => item.type === "video"),
      document: mediaItems.filter(item => item.type === "document"),
    };
  }, [mediaItems]);

  const tabs = [
    {
      key: "image" as const,
      label: "Hình ảnh",
      icon: ImageIcon,
      count: groupedMedia.image.length,
      color: "text-green-600",
    },
    {
      key: "audio" as const,
      label: "Âm thanh",
      icon: Music,
      count: groupedMedia.audio.length,
      color: "text-blue-600",
    },
    {
      key: "video" as const,
      label: "Video",
      icon: Video,
      count: groupedMedia.video.length,
      color: "text-purple-600",
    },
    {
      key: "document" as const,
      label: "Tài liệu",
      icon: FileText,
      count: groupedMedia.document.length,
      color: "text-orange-600",
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "image":
        return groupedMedia.image.length > 0 ? (
          <ImagePreviewGrid 
            images={groupedMedia.image.map(item => ({
              id: item.id,
              url: item.url,
              name: item.name,
              size: item.size,
              uploadedAt: item.uploadedAt,
            }))}
          />
        ) : (
          <div className="text-center py-12 text-gray-500">
            <ImageIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>Không có hình ảnh nào</p>
          </div>
        );

      case "audio":
        return groupedMedia.audio.length > 0 ? (
          <AudioPreviewPlayer 
            audioFiles={groupedMedia.audio.map(item => ({
              id: item.id,
              url: item.url,
              name: item.name,
              duration: item.duration,
              size: item.size,
              uploadedAt: item.uploadedAt,
            }))}
          />
        ) : (
          <div className="text-center py-12 text-gray-500">
            <Music className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>Không có file âm thanh nào</p>
          </div>
        );

      case "video":
        return groupedMedia.video.length > 0 ? (
          <VideoPreviewPlayer 
            videos={groupedMedia.video.map(item => ({
              id: item.id,
              url: item.url,
              name: item.name,
              duration: item.duration,
              size: item.size,
              thumbnail: item.thumbnail,
              uploadedAt: item.uploadedAt,
            }))}
          />
        ) : (
          <div className="text-center py-12 text-gray-500">
            <Video className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>Không có video nào</p>
          </div>
        );

      case "document":
        return (
          <div className="space-y-4">
            {groupedMedia.document.length > 0 ? (
              groupedMedia.document.map((doc) => (
                <div key={doc.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <div className="flex items-center gap-3">
                    <FileText className="w-8 h-8 text-orange-600" />
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{doc.name}</h3>
                      <div className="text-sm text-gray-500">
                        {doc.size && <span>{Math.round(doc.size / 1024)} KB</span>}
                        {doc.uploadedAt && (
                          <span className="ml-2">• {new Date(doc.uploadedAt).toLocaleDateString()}</span>
                        )}
                      </div>
                    </div>
                    <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                      Xem
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-12 text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Không có tài liệu nào</p>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn("bg-white rounded-lg border border-gray-200", className)}>
      {/* Tab Headers */}
      <div className="border-b border-gray-200">
        <div className="flex">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.key;
            
            return (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={cn(
                  "flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors",
                  isActive
                    ? "border-blue-500 text-blue-600 bg-blue-50"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                )}
              >
                <Icon className={cn("w-4 h-4", isActive ? tab.color : "text-gray-400")} />
                <span>{tab.label}</span>
                {tab.count > 0 && (
                  <span className={cn(
                    "px-2 py-0.5 text-xs rounded-full",
                    isActive 
                      ? "bg-blue-100 text-blue-600" 
                      : "bg-gray-100 text-gray-600"
                  )}>
                    {tab.count}
                  </span>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {renderTabContent()}
      </div>
    </div>
  );
}
