import React from "react";
import { cn } from "@/lib/utils";
import { KeywordForm } from "@/components/molecules/keyword-form";
import { HierarchicalContent } from "@/components/molecules/hierarchical-content";
import { Steps, StepItem } from "@/components/ui/steps";
import { Button } from "@/components/ui/Button";
import { Droppable } from "@hello-pangea/dnd";
import { FileText, ChevronLeft, ChevronRight, Plus } from "lucide-react";

interface Step {
  id: string;
  title: string;
  content?: string;
  nodeType?: string;
  children?: any[];
}

interface StepContentProps {
  step: Step;
  formData: Record<string, string>;
  onFormDataChange: (keywordId: string, value: string) => void;
  currentStep: number;
  totalSteps: number;
  allSteps?: Step[];
  onStepChange?: (stepIndex: number) => void;
  onPrevious?: () => void;
  onNext?: () => void;
  canGoNext?: boolean;
  mergedComponents?: any[];
  onDeleteComponent?: (componentId: string, staticComponent?: any) => void;
  isEditMode?: boolean;
  className?: string;
}

export function StepContent({
  step,
  formData,
  onFormDataChange,
  currentStep,
  totalSteps,
  allSteps = [],
  onStepChange,
  onPrevious,
  onNext,
  canGoNext = true,
  mergedComponents = [],
  onDeleteComponent,
  isEditMode = false,
  className,
}: StepContentProps) {
  console.log("🎯 StepContent rendered with:", {
    stepId: step?.id,
    stepTitle: step?.title,
    stepType: step?.type,
    stepNodeType: step?.nodeType,
    droppableId: `form-${step?.id}`,
    mergedComponentsLength: mergedComponents?.length || 0,
    formDataKeys: Object.keys(formData || {}),
  });

  return (
    <div className={cn("flex-1 flex flex-col min-h-0", className)}>
      {/* Step Header */}
      <div className="bg-white border-b border-gray-200 px-8 py-4 flex-shrink-0">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="text-sm text-gray-500 font-calsans">
                  Bước {currentStep + 1} / {totalSteps}
                </div>
                <div className="h-4 w-px bg-gray-300" />
                <h1 className="text-xl font-calsans text-gray-900">
                  {step?.title}
                </h1>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex items-center gap-3">
              <Button
                onClick={onPrevious}
                variant="outline"
                disabled={currentStep === 0}
                className="flex items-center gap-2 font-questrial"
              >
                <ChevronLeft className="w-4 h-4" />
                Quay lại
              </Button>

              <Button
                onClick={onNext}
                variant="default"
                disabled={!canGoNext}
                className="flex items-center gap-2 font-questrial"
              >
                {currentStep === totalSteps - 1 ? "Hoàn thành" : "Tiếp tục"}
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {step?.content && (
            <p className="text-gray-600 font-questrial mt-2">{step.content}</p>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-y-auto bg-gray-50 min-h-0">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            {/* Content */}
            <div className="p-8">
              <HierarchicalContent
                stepId={step?.id || ""}
                components={mergedComponents}
                formData={formData}
                onFormDataChange={onFormDataChange}
                onDeleteComponent={onDeleteComponent}
                isEditMode={isEditMode}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
