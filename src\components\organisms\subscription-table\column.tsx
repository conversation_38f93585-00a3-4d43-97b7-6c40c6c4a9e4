import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { SubscriptionResponse } from "@/types";

export const subscriptionColumns: ColumnDef<SubscriptionResponse>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "<PERSON>ói dịch vụ",
    cell: ({ row }) => <span className="font-medium">{row.original.name}</span>,
  },
  {
    accessorKey: "duration_months",
    header: "Th<PERSON><PERSON> hạn (tháng)",
    cell: ({ row }) => <span>{row.original.duration_months} tháng</span>,
  },
  {
    accessorKey: "price",
    header: "Giá (VNĐ)",
    cell: ({ row }) => <span>{row.original.price.toLocaleString()} đ</span>,
  },
  {
    accessorKey: "updated_at",
    header: "Ngày cập nhật",
    cell: ({ row }) => <span>{row.original.updated_at}</span>,
  },
  {
    accessorKey: "status",
    header: "Trạng thái",
    cell: ({ row }) => (
      <span>
        {row.original.status === "ACTIVE"
          ? "Đang hoạt động"
          : "Ngưng hoạt động"}
      </span>
    ),
  },
];
