"use client";

import React, { useState, useCallback, useEffect, useRef } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Plus, Trash2, Edit3, Image as ImageIcon, FileText } from "lucide-react";
import { cn } from "@/lib/utils";
import { Modal } from "@/components/ui/modal";
import { ResourceModal, ResourceData } from "@/components/molecules/resource-modal";
import { ImagePreview } from "@/components/molecules/image-preview";

interface TableCell {
  id: string;
  content: string;
  isHeader?: boolean;
  resourceData?: any; // For storing learning materials data
}

interface TableRow {
  id: string;
  cells: TableCell[];
}

interface TableData {
  rows: TableRow[];
  columns: number;
}

interface RichTableProps {
  data?: TableData;
  onChange?: (data: TableData) => void;
  className?: string;
}

// Default table data
const getDefaultTableData = (): TableData => ({
  rows: [
    {
      id: "header-row",
      cells: [
        { id: "h1", content: "Cột 1", isHeader: true },
        { id: "h2", content: "Cột 2", isHeader: true },
        { id: "h3", content: "Cột 3", isHeader: true },
      ],
    },
    {
      id: "row-1",
      cells: [
        { id: "r1c1", content: "<p>Nội dung ô 1</p>", resourceData: null },
        { id: "r1c2", content: "<p>Nội dung ô 2</p>", resourceData: null },
        { id: "r1c3", content: "<p>Nội dung ô 3</p>", resourceData: null },
      ],
    },
    {
      id: "row-2",
      cells: [
        { id: "r2c1", content: "<p>Nội dung ô 4</p>", resourceData: null },
        { id: "r2c2", content: "<p>Nội dung ô 5</p>", resourceData: null },
        { id: "r2c3", content: "<p>Nội dung ô 6</p>", resourceData: null },
      ],
    },
  ],
  columns: 3,
});

export function RichTable({ data, onChange, className }: RichTableProps) {
  const [tableData, setTableData] = useState<TableData>(
    data || getDefaultTableData()
  );
  const [editingCell, setEditingCell] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState("");
  const [showResourceModal, setShowResourceModal] = useState(false);
  const [currentCellForResource, setCurrentCellForResource] = useState<{rowId: string, cellId: string} | null>(null);
  const isInitialMount = useRef(true);

  // Update parent when data changes (debounced)
  useEffect(() => {
    // Don't trigger onChange on initial mount
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    const timeoutId = setTimeout(() => {
      console.log("RichTable - Saving data:", tableData);
      onChange?.(tableData);
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [tableData, onChange]);

  // Initialize table data from props on mount only
  useEffect(() => {
    if (data) {
      console.log("RichTable - Initializing with data:", data);
      setTableData(data);
    }
  }, []); // Only run on mount

  const updateCell = useCallback((rowId: string, cellId: string, content: string) => {
    setTableData((prev) => ({
      ...prev,
      rows: prev.rows.map((row) =>
        row.id === rowId
          ? {
              ...row,
              cells: row.cells.map((cell) =>
                cell.id === cellId ? { ...cell, content } : cell
              ),
            }
          : row
      ),
    }));
  }, []);

  const updateCellResource = useCallback((rowId: string, cellId: string, resourceData: any) => {
    setTableData((prev) => ({
      ...prev,
      rows: prev.rows.map((row) =>
        row.id === rowId
          ? {
              ...row,
              cells: row.cells.map((cell) =>
                cell.id === cellId ? { ...cell, resourceData } : cell
              ),
            }
          : row
      ),
    }));
  }, []);

  const handleResourceSubmit = async (resource: ResourceData) => {
    if (!currentCellForResource) return;

    // Convert File to base64 for storage
    if (resource.type === "image" && resource.file) {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result as string;
        const resourceWithBase64 = {
          ...resource,
          file: {
            name: resource.file!.name,
            size: resource.file!.size,
            type: resource.file!.type,
            base64: base64,
          },
        };
        updateCellResource(currentCellForResource.rowId, currentCellForResource.cellId, resourceWithBase64);
      };
      reader.readAsDataURL(resource?.file);
    } else {
      updateCellResource(currentCellForResource.rowId, currentCellForResource.cellId, resource);
    }

    setShowResourceModal(false);
    setCurrentCellForResource(null);
  };

  const openResourceModal = (rowId: string, cellId: string) => {
    setCurrentCellForResource({ rowId, cellId });
    setShowResourceModal(true);
  };

  const addRow = useCallback(() => {
    const newRowId = `row-${Date.now()}`;
    const newCells: TableCell[] = Array.from({ length: tableData.columns }, (_, i) => ({
      id: `${newRowId}-c${i}`,
      content: "<p>Nội dung mới</p>",
      resourceData: null,
    }));

    setTableData((prev) => ({
      ...prev,
      rows: [...prev.rows, { id: newRowId, cells: newCells }],
    }));
  }, [tableData.columns]);

  const addColumn = useCallback(() => {
    setTableData((prev) => ({
      ...prev,
      columns: prev.columns + 1,
      rows: prev.rows.map((row, rowIndex) => ({
        ...row,
        cells: [
          ...row.cells,
          {
            id: `${row.id}-c${prev.columns}`,
            content: rowIndex === 0 ? `Cột ${prev.columns + 1}` : "<p>Nội dung mới</p>",
            isHeader: rowIndex === 0,
            resourceData: rowIndex === 0 ? undefined : null,
          },
        ],
      })),
    }));
  }, []);

  const removeRow = useCallback((rowId: string) => {
    if (tableData.rows.length <= 1) return;
    setTableData((prev) => ({
      ...prev,
      rows: prev.rows.filter((row) => row.id !== rowId),
    }));
  }, [tableData.rows.length]);

  const removeColumn = useCallback((columnIndex: number) => {
    if (tableData.columns <= 1) return;
    setTableData((prev) => ({
      ...prev,
      columns: prev.columns - 1,
      rows: prev.rows.map((row) => ({
        ...row,
        cells: row.cells.filter((_, index) => index !== columnIndex),
      })),
    }));
  }, [tableData.columns]);

  const openRichEditor = useCallback((cellId: string, currentContent: string) => {
    setEditingCell(cellId);
    setEditingContent(currentContent);
  }, []);

  const saveRichEdit = useCallback((content: string) => {
    if (editingCell) {
      console.log("Saving rich edit:", { editingCell, content });

      // Find the row that contains this cell
      const row = tableData.rows.find(row =>
        row.cells.some(cell => cell.id === editingCell)
      );

      if (row) {
        console.log("Found row:", row.id);
        updateCell(row.id, editingCell, content);
      }

      setEditingCell(null);
      setEditingContent("");
    }
  }, [editingCell, updateCell, tableData.rows]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Table Controls */}
      <div className="flex gap-2">
        <Button size="sm" onClick={addRow} variant="outline">
          <Plus className="w-4 h-4 mr-1" />
          Thêm hàng
        </Button>
        <Button size="sm" onClick={addColumn} variant="outline">
          <Plus className="w-4 h-4 mr-1" />
          Thêm cột
        </Button>
      </div>

      {/* Table */}
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <table className="w-full">
          <tbody>
            {tableData.rows.map((row, rowIndex) => (
              <tr key={row.id} className={rowIndex === 0 ? "bg-gray-50" : ""}>
                {row.cells.map((cell, cellIndex) => (
                  <td
                    key={cell.id}
                    className={cn(
                      "border-r border-b border-gray-200 p-3 min-w-[200px] relative group",
                      cell.isHeader && "font-medium bg-gray-50"
                    )}
                  >
                    <div className="min-h-[40px] flex flex-col gap-2">
                      {cell.isHeader ? (
                        // Header cells - simple input
                        <Input
                          value={cell.content}
                          onChange={(e) => updateCell(row.id, cell.id, e.target.value)}
                          className="border-0 bg-transparent font-medium"
                          placeholder="Tiêu đề cột"
                        />
                      ) : (
                        // Content cells - rich text preview and resource display
                        <div className="space-y-2">
                          {/* Text content */}
                          <div
                            className="flex-1 cursor-pointer min-h-[40px] p-2 rounded hover:bg-gray-50"
                            onClick={() => openRichEditor(cell.id, cell.content)}
                            dangerouslySetInnerHTML={{ __html: cell.content || "<p>Nhấn để chỉnh sửa...</p>" }}
                          />

                          {/* Resource display */}
                          {cell.resourceData && (
                            <div className="mt-2">
                              {cell.resourceData.type === "image" ? (
                                cell.resourceData.file ? (
                                  <ImagePreview
                                    file={cell.resourceData.file}
                                    onRemove={() => updateCellResource(row.id, cell.id, null)}
                                    className="w-full max-w-[150px]"
                                  />
                                ) : cell.resourceData.url ? (
                                  <div className="relative w-full max-w-[150px]">
                                    <img
                                      src={cell.resourceData.url}
                                      alt={cell.resourceData.description || "Selected image"}
                                      className="w-full h-24 object-cover rounded border border-gray-200"
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5YTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvaSBoaW5oIGFuaDwvdGV4dD48L3N2Zz4=";
                                      }}
                                    />
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => updateCellResource(row.id, cell.id, null)}
                                      className="absolute top-1 right-1 bg-white/80 hover:bg-white text-red-600 hover:text-red-700 rounded-full p-1 h-6 w-6"
                                    >
                                      ✕
                                    </Button>
                                  </div>
                                ) : null
                              ) : (
                                <div className="flex items-center gap-2 p-2 border border-gray-200 rounded text-sm">
                                  <FileText className="w-4 h-4 text-gray-500" />
                                  <span className="flex-1 truncate">
                                    {cell.resourceData.description || cell.resourceData.file?.name || "Tài liệu"}
                                  </span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => updateCellResource(row.id, cell.id, null)}
                                    className="text-red-600 hover:text-red-700 h-6 w-6 p-0"
                                  >
                                    ✕
                                  </Button>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}

                      <div className="flex gap-1 justify-end">
                        {!cell.isHeader && (
                          <>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => openRichEditor(cell.id, cell.content)}
                              className="h-7 w-7 p-0 bg-blue-100 hover:bg-blue-200 text-blue-600"
                              title="Rich Text Editor"
                            >
                              <Edit3 className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => openResourceModal(row.id, cell.id)}
                              className="h-7 w-7 p-0 bg-green-100 hover:bg-green-200 text-green-600"
                              title="Thêm học liệu"
                            >
                              <ImageIcon className="w-4 h-4" />
                            </Button>
                          </>
                        )}
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeColumn(cellIndex)}
                          className="h-7 w-7 p-0 bg-red-100 hover:bg-red-200 text-red-600"
                          title="Xóa cột"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </td>
                ))}
                {!row.cells[0]?.isHeader && (
                  <td className="border-b border-gray-200 p-2 w-12 text-center">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeRow(row.id)}
                      className="h-7 w-7 p-0 bg-red-100 hover:bg-red-200 text-red-600"
                      title="Xóa hàng"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Rich Text Editor Modal */}
      <Modal
        isOpen={!!editingCell}
        onClose={() => setEditingCell(null)}
        title="Chỉnh sửa nội dung ô"
        size="xl"
      >
        <RichTextEditor
          content={editingContent}
          onChange={setEditingContent}
          className="mb-4"
        />
        <div className="flex gap-2 justify-end mt-4">
          <Button variant="outline" onClick={() => setEditingCell(null)}>
            Hủy
          </Button>
          <Button onClick={() => saveRichEdit(editingContent)}>
            Lưu
          </Button>
        </div>
      </Modal>

      {/* Resource Modal */}
      <ResourceModal
        isOpen={showResourceModal}
        onClose={() => {
          setShowResourceModal(false);
          setCurrentCellForResource(null);
        }}
        onSubmit={handleResourceSubmit}
      />
    </div>
  );
}
