/**
 * API Endpoints Constants
 * Centralized management of all API endpoints
 */

export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: "/auth-service-local/api/login",
    LOGIN_GOOGLE: "/auth-service-local/api/login-google",
  },

  // Academic Years
  ACADEMIC_YEARS: "/academic-years",

  // Books
  BOOKS: "/books",
  BOOKS_BY_SUBJECT: "/books/by-subject",

  // Book Types
  BOOK_TYPES: "/book-types",

  // Chapters
  CHAPTERS: "/chapters",
  CHAPTERS_BY_BOOK: "/chapters/by-book",

  // Grades
  GRADES: "/grades",

  // Lessons
  LESSONS: "/lessons",
  LESSONS_BY_CHAPTER: "/lessons/by-chapter",

  // Subjects
  SUBJECTS: "/subjects",
  SUBJECTS_BY_GRADE: "/subjects/by-grade",

  // Forms (Lesson Plans)
  FORMS: "/forms",

  // Lesson Plan Templates
  LESSON_PLANS: "/lesson-plan-service-local/api/lesson-plans",

  LESSON_NODES: "/lesson-plan-service-local/api/lesson-nodes",
  LESSON_NODES_TREE: (id: string) =>
    `/lesson-plan-service-local/api/lesson-nodes/${id}/tree`,

  LESSON_NODE_CHIDREN: (nodeId: string) =>
    `/lesson-plan-service-local/api/lesson-nodes/${nodeId}/children`,

  // Tags
  TAGS: "/academic-resource-service-local/api/tags",

  //ACADEMIC RESOURCE
  ACADEMIC_RESOURCE: "academic-resource-service-local/api/academic-resources",
   //ACADEMIC RESOURCE SEARCH 
   ACADEMIC_RESOURCE_SEARCH: "academic-resource-service-local/api/academic-resources/search",
  ACADEMIC_RESOURCE_UPLOAD:
    "/academic-resource-service-local/api/academic-resources/upload",
} as const;

// PDF API Endpoints (Secondary API - Port 8000)
export const PDF_API_ENDPOINTS = {
  // Textbook management
  GET_ALL_TEXTBOOKS: "/pdf/getAllTextBook",
  GET_TEXTBOOK_BY_ID: (id: string) => `/pdf/getTextBook/${id}`,
  UPLOAD_TEXTBOOK: "/pdf/upload",
  DELETE_TEXTBOOK: (id: string) => `/pdf/deleteTextBook/${id}`,

  // Quick analysis
  QUICK_TEXTBOOK_ANALYSIS: "/pdf/quick-textbook-analysis",

  //Task-progress
  TASKS_STATUS: `/tasks/status`,
} as const;

export const EXAM_ENDPOINTS = {
  GENERATE_EXAM: `/exam/generate-exam`,
  GENERATE_SMART_EXAM: `/exam/generate-smart-exam`,
  EXAM_IMPORT: `/exam/import-docx`,
} as const;

export const LESSON_FRAMEWORK_ENDPOINTS = {
  //UPLOAD LESON PLAN FRAMEWORK
  LESSON_PLAN_FRAMEWORK: `/lesson/lesson-plan-framework`,
} as const;

// Combined endpoints for easy access
export const ALL_API_ENDPOINTS = {
  MAIN: API_ENDPOINTS,
  PDF: PDF_API_ENDPOINTS,
} as const;

// Type for API endpoints (optional, for better TypeScript support)
export type ApiEndpoint = (typeof API_ENDPOINTS)[keyof typeof API_ENDPOINTS];
export type PdfApiEndpoint =
  (typeof PDF_API_ENDPOINTS)[keyof typeof PDF_API_ENDPOINTS];
