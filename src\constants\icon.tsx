export const FormIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="44"
    height="44"
    viewBox="0 0 44 44"
    fill="none"
  >
    <path
      opacity="0.4"
      d="M27.1151 3.66675H16.8851C8.78174 3.66675 8.78174 7.97508 8.78174 11.7701V22.3851C8.78174 22.7884 8.96507 23.1551 9.27674 23.3934C9.5884 23.6317 10.0101 23.7234 10.3951 23.6134C11.2201 23.3934 12.2467 23.2834 13.4751 23.2834C14.7034 23.2834 14.9601 23.4301 15.6934 23.9801L17.3617 25.7401C18.5534 27.0234 20.2584 27.7567 22.0184 27.7567C23.7784 27.7567 25.4651 27.0234 26.6751 25.7401L28.3434 23.9801C29.0767 23.4301 29.3334 23.2834 30.5617 23.2834C31.7901 23.2834 32.8167 23.3934 33.6417 23.6134C34.0267 23.7234 34.4301 23.6317 34.7601 23.3934C35.0717 23.1551 35.2551 22.7701 35.2551 22.3851V11.7701C35.2184 7.97508 35.2184 3.66675 27.1151 3.66675Z"
      fill="#859BFF"
    />
    <path
      opacity="0.4"
      d="M27.5367 32.4501C27.5367 33.1651 26.9684 33.7334 26.2717 33.7334H17.7284C17.0317 33.7334 16.4634 33.1651 16.4634 32.4501C16.4634 31.7351 17.0317 31.1667 17.7284 31.1667H26.2717C26.9684 31.1667 27.5367 31.7351 27.5367 32.4501Z"
      fill="#859BFF"
    />
    <path
      d="M34.2649 21.1382C33.2199 20.8632 31.9916 20.7166 30.5249 20.7166C28.4899 20.7166 27.7382 21.2116 26.6932 21.9999C26.6382 22.0366 26.5832 22.0916 26.5282 22.1466L24.7866 23.9982C23.3199 25.5382 20.6799 25.5566 19.2132 23.9799L17.4716 22.1466C17.4166 22.0916 17.3616 22.0366 17.3066 21.9999C16.2616 21.2116 15.5099 20.7166 13.4749 20.7166C12.0082 20.7166 10.7799 20.8632 9.73491 21.1382C5.37158 22.3116 5.37158 25.7766 5.37158 28.8199V30.5249C5.37158 35.1266 5.37158 40.3332 15.1799 40.3332H28.8199C35.3282 40.3332 38.6282 37.0332 38.6282 30.5249V28.8199C38.6282 25.7766 38.6282 22.3116 34.2649 21.1382ZM26.2716 33.7332H17.7282C17.0316 33.7332 16.4632 33.1649 16.4632 32.4499C16.4632 31.7349 17.0316 31.1666 17.7282 31.1666H26.2716C26.9682 31.1666 27.5366 31.7349 27.5366 32.4499C27.5366 33.1649 26.9682 33.7332 26.2716 33.7332Z"
      fill="#007EF3"
    />
  </svg>
);

export const ExamIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="45"
    height="44"
    viewBox="0 0 45 44"
    fill="none"
  >
    <path
      opacity="0.4"
      d="M40.1098 19.1399L38.3132 26.8033C36.7732 33.4216 33.7298 36.0983 28.0098 35.5483C27.0932 35.4749 26.1032 35.3099 25.0398 35.0533L21.9598 34.3199C14.3148 32.5049 11.9498 28.7283 13.7465 21.0649L15.5432 13.3833C15.9098 11.8249 16.3498 10.4683 16.8998 9.34993C19.0448 4.91327 22.6932 3.7216 28.8165 5.16993L31.8782 5.88493C39.5598 7.6816 41.9065 11.4766 40.1098 19.1399Z"
      fill="#FFEC3D"
    />
    <path
      d="M28.0098 35.5482C26.8731 36.3182 25.4431 36.9599 23.7015 37.5282L20.8048 38.4815C13.5265 40.8282 9.6948 38.8665 7.3298 31.5882L4.98313 24.3465C2.63646 17.0682 4.57979 13.2182 11.8581 10.8715L14.7548 9.91819C15.5065 9.67985 16.2215 9.47819 16.8998 9.34985C16.3498 10.4682 15.9098 11.8249 15.5431 13.3832L13.7465 21.0649C11.9498 28.7282 14.3148 32.5049 21.9598 34.3199L25.0398 35.0532C26.1031 35.3099 27.0931 35.4749 28.0098 35.5482Z"
      fill="#FF5812"
    />
    <path
      d="M32.4649 19.2684C32.3549 19.2684 32.2449 19.2501 32.1166 19.2317L23.2249 16.9767C22.4916 16.7934 22.0516 16.0417 22.2349 15.3084C22.4182 14.5751 23.1699 14.1351 23.9032 14.3184L32.7949 16.5734C33.5282 16.7567 33.9682 17.5084 33.7849 18.2417C33.6382 18.8467 33.0699 19.2684 32.4649 19.2684Z"
      fill="#FACD05"
    />
    <path
      d="M27.0933 25.4651C26.9833 25.4651 26.8733 25.4467 26.745 25.4284L21.41 24.0717C20.6766 23.8884 20.2366 23.1367 20.42 22.4034C20.6033 21.6701 21.355 21.2301 22.0883 21.4134L27.4233 22.77C28.1566 22.9534 28.5966 23.705 28.4133 24.4384C28.2666 25.0617 27.7166 25.4651 27.0933 25.4651Z"
      fill="#FAE205"
    />
  </svg>
);

export const SlideIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="45"
    height="44"
    viewBox="0 0 45 44"
    fill="none"
  >
    <path
      opacity="0.4"
      d="M27.5667 38.9584H22.3417V33.7334C22.3417 32.9817 21.7184 32.3584 20.9667 32.3584C20.215 32.3584 19.5917 32.9817 19.5917 33.7334V38.9584H14.3667C13.615 38.9584 12.9917 39.5817 12.9917 40.3334C12.9917 41.085 13.615 41.7084 14.3667 41.7084H27.5667C28.3184 41.7084 28.9417 41.085 28.9417 40.3334C28.9417 39.5817 28.3184 38.9584 27.5667 38.9584Z"
      fill="#001A7E"
    />
    <path
      d="M37.4668 22.3666V25.4833C37.4668 31.2583 34.1668 33.7333 29.2168 33.7333H12.7168C7.7668 33.7333 4.4668 31.2583 4.4668 25.4833V15.5833C4.4668 9.80825 7.7668 7.33325 12.7168 7.33325H17.6668C17.4285 8.02992 17.3001 8.79992 17.3001 9.62492V16.775C17.3001 18.5533 17.8868 20.0566 18.9318 21.1016C19.9768 22.1466 21.4801 22.7333 23.2585 22.7333V25.2815C23.2585 26.2165 24.3218 26.785 25.1101 26.2716L30.4085 22.7333H35.1751C36.0001 22.7333 36.7701 22.6049 37.4668 22.3666Z"
      fill="#33E1FF"
    />
    <path
      opacity="0.4"
      d="M41.1334 9.62508V16.7751C41.1334 19.5068 39.7401 21.5601 37.4667 22.3668C36.7701 22.6051 36 22.7335 35.175 22.7335H30.4084L25.11 26.2718C24.3217 26.7851 23.2584 26.2167 23.2584 25.2817V22.7335C21.48 22.7335 19.9767 22.1468 18.9317 21.1018C17.8867 20.0568 17.3 18.5535 17.3 16.7751V9.62508C17.3 8.80008 17.4284 8.03008 17.6667 7.33341C18.4734 5.06008 20.5267 3.66675 23.2584 3.66675H35.175C38.75 3.66675 41.1334 6.05008 41.1334 9.62508Z"
      fill="#E6EBFF"
    />
    <path
      d="M29.1067 14.6667C28.3367 14.6667 27.7317 14.0434 27.7317 13.2917C27.7317 12.5401 28.355 11.9167 29.1067 11.9167C29.8584 11.9167 30.4817 12.5401 30.4817 13.2917C30.4817 14.0434 29.8584 14.6667 29.1067 14.6667Z"
      fill="#29322C"
    />
    <path
      d="M34.2583 14.6667C33.4883 14.6667 32.8833 14.0434 32.8833 13.2917C32.8833 12.5401 33.5066 11.9167 34.2583 11.9167C35.01 11.9167 35.6333 12.5401 35.6333 13.2917C35.6333 14.0434 35.01 14.6667 34.2583 14.6667Z"
      fill="#29322C"
    />
    <path
      d="M23.9368 14.6667C23.1668 14.6667 22.5618 14.0434 22.5618 13.2917C22.5618 12.5401 23.1851 11.9167 23.9368 11.9167C24.6884 11.9167 25.3118 12.5401 25.3118 13.2917C25.3118 14.0434 24.7068 14.6667 23.9368 14.6667Z"
      fill="#29322C"
    />
  </svg>
);

export const LessonPlanIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="45"
    height="44"
    viewBox="0 0 45 44"
    fill="none"
  >
    <path
      opacity="0.4"
      d="M29.8816 3.66675H14.5366C7.84491 3.66675 3.86658 7.64508 3.86658 14.3184V29.6634C3.86658 36.3367 7.84491 40.3151 14.5182 40.3151H29.8632C36.5366 40.3151 40.5149 36.3367 40.5149 29.6634V14.3184C40.5332 7.64508 36.5549 3.66675 29.8816 3.66675Z"
      fill="#A8EA00"
    />
    <path
      d="M29.882 3.66675C29.9185 3.66675 29.955 3.66846 29.9913 3.6687V22.7703C29.9913 23.9435 29.6432 24.8233 29.0382 25.1716C28.4149 25.5383 27.4252 25.3549 26.3253 24.6951L23.9054 23.2468C22.9705 22.6785 21.4303 22.6786 20.4952 23.2468L18.0753 24.6951C16.9753 25.3551 15.9848 25.52 15.3615 25.1716C14.7566 24.8233 14.4084 23.9434 14.4083 22.7703V3.6687C14.4509 3.66838 14.4935 3.66675 14.5363 3.66675H29.882Z"
      fill="#06B460"
    />
  </svg>
);

export const PenIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="45"
    height="44"
    viewBox="0 0 45 44"
    fill="none"
  >
    <path
      d="M37.3033 19.6167L32.17 22.8617L21.7933 12.485L25.02 7.35169C26.5233 4.98669 29.53 4.87669 31.7483 7.07669L37.5783 12.9067C39.65 14.9784 39.5217 18.205 37.3033 19.6167Z"
      fill="#5726CA"
    />
    <path
      opacity="0.4"
      d="M27.5683 36.575L11.5999 38.4633C10.4999 38.5916 9.50992 38.4266 8.68492 38.0233C7.78658 37.5833 7.08992 36.8866 6.64992 35.9883C6.24658 35.1633 6.09992 34.1733 6.20992 33.0916L8.11658 17.105C8.57492 13.1083 10.0599 11.8066 14.2766 12.045L21.8299 12.5033L32.2066 22.88L32.6466 30.4333C33.0316 34.65 31.5833 36.1166 27.5683 36.575Z"
      fill="#C7C1FF"
    />
    <path
      d="M15.2299 31.4417L8.66659 38.005C7.76826 37.565 7.07159 36.8683 6.63159 35.97L13.2133 29.3883C13.7816 28.8383 14.6983 28.8383 15.2483 29.3883C15.8166 29.975 15.8166 30.8733 15.2299 31.4417Z"
      fill="#090069"
    />
  </svg>
);

export const HistoryIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M11.5933 10.4467L8.89999 8H7.09332L4.39999 10.4467C3.64665 11.1267 3.39999 12.1733 3.76665 13.12C4.13332 14.06 5.02665 14.6667 6.03332 14.6667H9.95999C10.9733 14.6667 11.86 14.06 12.2267 13.12C12.5933 12.1733 12.3467 11.1267 11.5933 10.4467ZM9.21332 12.0933H6.78665C6.53332 12.0933 6.33332 11.8867 6.33332 11.64C6.33332 11.3933 6.53999 11.1867 6.78665 11.1867H9.21332C9.46665 11.1867 9.66665 11.3933 9.66665 11.64C9.66665 11.8867 9.45999 12.0933 9.21332 12.0933Z"
      fill="#292D32"
    />
    <path
      d="M12.2333 2.87992C11.8666 1.93992 10.9733 1.33325 9.96661 1.33325H6.03328C5.02661 1.33325 4.13328 1.93992 3.76661 2.87992C3.40661 3.82659 3.65328 4.87325 4.40661 5.55325L7.09994 7.99992H8.90661L11.5999 5.55325C12.3466 4.87325 12.5933 3.82659 12.2333 2.87992ZM9.21328 4.81992H6.78661C6.53328 4.81992 6.33328 4.61325 6.33328 4.36659C6.33328 4.11992 6.53995 3.91325 6.78661 3.91325H9.21328C9.46661 3.91325 9.66661 4.11992 9.66661 4.36659C9.66661 4.61325 9.45994 4.81992 9.21328 4.81992Z"
      fill="#292D32"
    />
  </svg>
);

export const SearchIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="19"
    height="18"
    viewBox="0 0 19 18"
    fill="none"
  >
    <path
      d="M8.8136 15.5625C4.7786 15.5625 1.5011 12.285 1.5011 8.25C1.5011 4.215 4.7786 0.9375 8.8136 0.9375C12.8486 0.9375 16.1261 4.215 16.1261 8.25C16.1261 12.285 12.8486 15.5625 8.8136 15.5625ZM8.8136 2.0625C5.4011 2.0625 2.6261 4.8375 2.6261 8.25C2.6261 11.6625 5.4011 14.4375 8.8136 14.4375C12.2261 14.4375 15.0011 11.6625 15.0011 8.25C15.0011 4.8375 12.2261 2.0625 8.8136 2.0625Z"
      fill="#292D32"
    />
    <path
      d="M15.6837 17.0925C15.6237 17.0925 15.5637 17.085 15.5112 17.0775C15.1587 17.0325 14.5212 16.7925 14.1612 15.72C13.9737 15.1575 14.0412 14.595 14.3487 14.1675C14.6562 13.74 15.1737 13.5 15.7662 13.5C16.5312 13.5 17.1312 13.7925 17.4012 14.31C17.6712 14.8275 17.5962 15.4875 17.1687 16.125C16.6362 16.9275 16.0587 17.0925 15.6837 17.0925ZM15.2337 15.3675C15.3612 15.7575 15.5412 15.9525 15.6612 15.9675C15.7812 15.9825 16.0062 15.84 16.2387 15.5025C16.4562 15.18 16.4712 14.9475 16.4187 14.8425C16.3662 14.7375 16.1562 14.625 15.7662 14.625C15.5337 14.625 15.3612 14.7 15.2637 14.8275C15.1737 14.955 15.1587 15.15 15.2337 15.3675Z"
      fill="#292D32"
    />
  </svg>
);

export const ListIcon = (active: boolean) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M13.2668 15.1667H2.7335C1.4535 15.1667 0.833496 14.5133 0.833496 13.18V10.4867C0.833496 9.14667 1.4535 8.5 2.7335 8.5H13.2668C14.5468 8.5 15.1668 9.15333 15.1668 10.4867V13.18C15.1668 14.5133 14.5468 15.1667 13.2668 15.1667ZM2.7335 9.5C2.06016 9.5 1.8335 9.64 1.8335 10.4867V13.18C1.8335 14.0267 2.06016 14.1667 2.7335 14.1667H13.2668C13.9402 14.1667 14.1668 14.0267 14.1668 13.18V10.4867C14.1668 9.64 13.9402 9.5 13.2668 9.5H2.7335Z"
      fill={active ? "#2B2B2B" : "#9B9B9B"}
    />
    <path
      d="M13.2668 7.49992H2.7335C1.4535 7.49992 0.833496 6.84659 0.833496 5.51325V2.81992C0.833496 1.47992 1.4535 0.833252 2.7335 0.833252H13.2668C14.5468 0.833252 15.1668 1.48659 15.1668 2.81992V5.51325C15.1668 6.84659 14.5468 7.49992 13.2668 7.49992ZM2.7335 1.83325C2.06016 1.83325 1.8335 1.97325 1.8335 2.81992V5.51325C1.8335 6.35992 2.06016 6.49992 2.7335 6.49992H13.2668C13.9402 6.49992 14.1668 6.35992 14.1668 5.51325V2.81992C14.1668 1.97325 13.9402 1.83325 13.2668 1.83325H2.7335Z"
      fill={active ? "#2B2B2B" : "#9B9B9B"}
    />
  </svg>
);

export const GridIcon = (active: boolean) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M13.18 7.49992H10.4867C9.14667 7.49992 8.5 6.90659 8.5 5.67992V2.65325C8.5 1.42659 9.15333 0.833252 10.4867 0.833252H13.18C14.52 0.833252 15.1667 1.42659 15.1667 2.65325V5.67325C15.1667 6.90659 14.5133 7.49992 13.18 7.49992ZM10.4867 1.83325C9.59333 1.83325 9.5 2.08659 9.5 2.65325V5.67325C9.5 6.24658 9.59333 6.49325 10.4867 6.49325H13.18C14.0733 6.49325 14.1667 6.23992 14.1667 5.67325V2.65325C14.1667 2.07992 14.0733 1.83325 13.18 1.83325H10.4867Z"
      fill={active ? "#2B2B2B" : "#9B9B9B"}
    />
    <path
      d="M13.18 15.1667H10.4867C9.14667 15.1667 8.5 14.5133 8.5 13.18V10.4867C8.5 9.14667 9.15333 8.5 10.4867 8.5H13.18C14.52 8.5 15.1667 9.15333 15.1667 10.4867V13.18C15.1667 14.5133 14.5133 15.1667 13.18 15.1667ZM10.4867 9.5C9.7 9.5 9.5 9.7 9.5 10.4867V13.18C9.5 13.9667 9.7 14.1667 10.4867 14.1667H13.18C13.9667 14.1667 14.1667 13.9667 14.1667 13.18V10.4867C14.1667 9.7 13.9667 9.5 13.18 9.5H10.4867Z"
      fill={active ? "#2B2B2B" : "#9B9B9B"}
    />
    <path
      d="M5.51337 7.49992H2.82004C1.48004 7.49992 0.833374 6.90659 0.833374 5.67992V2.65325C0.833374 1.42659 1.48671 0.833252 2.82004 0.833252H5.51337C6.85337 0.833252 7.50004 1.42659 7.50004 2.65325V5.67325C7.50004 6.90659 6.84671 7.49992 5.51337 7.49992ZM2.82004 1.83325C1.92671 1.83325 1.83337 2.08659 1.83337 2.65325V5.67325C1.83337 6.24658 1.92671 6.49325 2.82004 6.49325H5.51337C6.40671 6.49325 6.50004 6.23992 6.50004 5.67325V2.65325C6.50004 2.07992 6.40671 1.83325 5.51337 1.83325H2.82004Z"
      fill={active ? "#2B2B2B" : "#9B9B9B"}
    />
    <path
      d="M5.51337 15.1667H2.82004C1.48004 15.1667 0.833374 14.5133 0.833374 13.18V10.4867C0.833374 9.14667 1.48671 8.5 2.82004 8.5H5.51337C6.85337 8.5 7.50004 9.15333 7.50004 10.4867V13.18C7.50004 14.5133 6.84671 15.1667 5.51337 15.1667ZM2.82004 9.5C2.03337 9.5 1.83337 9.7 1.83337 10.4867V13.18C1.83337 13.9667 2.03337 14.1667 2.82004 14.1667H5.51337C6.30004 14.1667 6.50004 13.9667 6.50004 13.18V10.4867C6.50004 9.7 6.30004 9.5 5.51337 9.5H2.82004Z"
      fill={active ? "#2B2B2B" : "#9B9B9B"}
    />
  </svg>
);

export const CheckIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="14"
    height="15"
    viewBox="0 0 14 15"
    fill="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.9579 4.4057C12.1655 4.62373 12.157 4.96873 11.9389 5.17627L5.64064 11.1718C5.53308 11.2742 5.3884 11.3282 5.24006 11.3215C5.09172 11.3147 4.95255 11.2477 4.85474 11.136L2.43231 8.36885C2.23404 8.14236 2.25691 7.79801 2.4834 7.59974C2.7099 7.40146 3.05424 7.42433 3.25252 7.65083L5.30063 9.99041L11.1873 4.38672C11.4054 4.17917 11.7504 4.18767 11.9579 4.4057Z"
      fill="#1D4ED8"
    />
  </svg>
);

export const XIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
  >
    <path
      d="M10.6707 10.9214C10.8835 10.7085 10.8835 10.3634 10.6707 10.1506L7.9732 7.45312L10.6714 4.75491C10.8843 4.54206 10.8843 4.19696 10.6714 3.9841C10.4585 3.77125 10.1134 3.77125 9.90059 3.9841L7.20239 6.68231L4.50418 3.9841C4.29133 3.77125 3.94622 3.77125 3.73337 3.9841C3.52052 4.19696 3.52052 4.54206 3.73337 4.75491L6.43158 7.45312L3.73411 10.1506C3.52126 10.3634 3.52126 10.7085 3.73411 10.9214C3.94696 11.1342 4.29207 11.1342 4.50492 10.9214L7.20239 8.22393L9.89985 10.9214C10.1127 11.1342 10.4578 11.1342 10.6707 10.9214Z"
      fill="#191D23"
    />
  </svg>
);

export const CoppyIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="19"
    height="18"
    viewBox="0 0 19 18"
    fill="none"
  >
    <path
      d="M9.48762 17.0625H6.63762C3.34512 17.0625 1.87512 15.5925 1.87512 12.3V9.45C1.87512 6.1575 3.34512 4.6875 6.63762 4.6875H8.88762C9.19512 4.6875 9.45012 4.9425 9.45012 5.25C9.45012 5.5575 9.19512 5.8125 8.88762 5.8125H6.63762C3.95262 5.8125 3.00012 6.765 3.00012 9.45V12.3C3.00012 14.985 3.95262 15.9375 6.63762 15.9375H9.48762C12.1726 15.9375 13.1251 14.985 13.1251 12.3V10.05C13.1251 9.7425 13.3801 9.4875 13.6876 9.4875C13.9951 9.4875 14.2501 9.7425 14.2501 10.05V12.3C14.2501 15.5925 12.7801 17.0625 9.48762 17.0625Z"
      fill="#292D32"
    />
    <path
      d="M13.6876 10.6124H11.2876C9.18007 10.6124 8.32507 9.75737 8.32507 7.64987V5.24987C8.32507 5.02487 8.46007 4.81487 8.67007 4.73237C8.88007 4.64237 9.12007 4.69487 9.28507 4.85237L14.0851 9.65237C14.2426 9.80987 14.2951 10.0574 14.2051 10.2674C14.1226 10.4774 13.9126 10.6124 13.6876 10.6124ZM9.45007 6.60737V7.64987C9.45007 9.14237 9.79507 9.48737 11.2876 9.48737H12.3301L9.45007 6.60737Z"
      fill="#292D32"
    />
    <path
      d="M12.6376 2.0625H9.63757C9.33007 2.0625 9.07507 1.8075 9.07507 1.5C9.07507 1.1925 9.33007 0.9375 9.63757 0.9375H12.6376C12.9451 0.9375 13.2001 1.1925 13.2001 1.5C13.2001 1.8075 12.9451 2.0625 12.6376 2.0625Z"
      fill="#292D32"
    />
    <path
      d="M6.18762 4.3125C5.88012 4.3125 5.62512 4.0575 5.62512 3.75C5.62512 2.1975 6.88512 0.9375 8.43762 0.9375H10.4026C10.7101 0.9375 10.9651 1.1925 10.9651 1.5C10.9651 1.8075 10.7101 2.0625 10.4026 2.0625H8.43762C7.50762 2.0625 6.75012 2.82 6.75012 3.75C6.75012 4.0575 6.49512 4.3125 6.18762 4.3125Z"
      fill="#292D32"
    />
    <path
      d="M15.3301 13.3125C15.0226 13.3125 14.7676 13.0575 14.7676 12.75C14.7676 12.4425 15.0226 12.1875 15.3301 12.1875C16.1851 12.1875 16.8751 11.49 16.8751 10.6425V6C16.8751 5.6925 17.1301 5.4375 17.4376 5.4375C17.7451 5.4375 18.0001 5.6925 18.0001 6V10.6425C18.0001 12.1125 16.8001 13.3125 15.3301 13.3125Z"
      fill="#292D32"
    />
    <path
      d="M17.4376 6.56237H15.1876C13.1926 6.56237 12.3751 5.74487 12.3751 3.74987V1.49987C12.3751 1.27487 12.5101 1.06487 12.7201 0.982369C12.9301 0.892369 13.1701 0.94487 13.3351 1.10237L17.8351 5.60237C17.9926 5.75987 18.0451 6.00737 17.9551 6.21737C17.8726 6.42737 17.6626 6.56237 17.4376 6.56237ZM13.5001 2.85737V3.74987C13.5001 5.12237 13.8151 5.43737 15.1876 5.43737H16.0801L13.5001 2.85737Z"
      fill="#292D32"
    />
  </svg>
);
export const EditIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="19"
    height="19"
    viewBox="0 0 19 19"
    fill="none"
  >
    <path
      d="M5.77495 6.98627C5.72995 6.98627 5.68496 6.97875 5.63996 6.97125C5.49746 6.93375 5.36995 6.84378 5.29495 6.71628L4.12495 4.79628C3.83995 4.33128 3.74995 3.77627 3.88495 3.23627C4.01245 2.70377 4.34245 2.24626 4.81495 1.96126C5.27995 1.67626 5.83495 1.58625 6.37495 1.72125C6.91495 1.84875 7.36495 2.17874 7.64995 2.65124L8.81995 4.57129C8.98495 4.83379 8.89495 5.17877 8.63245 5.34377L6.06745 6.90378C5.97745 6.95628 5.87995 6.98627 5.77495 6.98627ZM5.87995 2.78629C5.70745 2.78629 5.54245 2.83128 5.39995 2.92128C5.18995 3.04878 5.03995 3.25875 4.97995 3.49875C4.91995 3.74625 4.95745 3.99375 5.08495 4.20375L5.96245 5.64378L7.56745 4.66875L6.68995 3.22876C6.56245 3.01876 6.35245 2.86876 6.11245 2.80876C6.02995 2.79376 5.95495 2.78629 5.87995 2.78629Z"
      fill="#292D32"
    />
    <path
      d="M9.50251 17.5691C9.27751 17.5691 9.05251 17.5391 8.83501 17.4716C8.17501 17.2766 7.64251 16.8266 7.33501 16.1891L5.79751 13.0541C4.98001 11.3891 5.55751 9.40912 7.14001 8.44162L9.51 7.00163C11.0925 6.03413 13.125 6.43161 14.2275 7.92411L16.305 10.7291C16.725 11.2991 16.8825 11.9816 16.755 12.6491C16.6275 13.3166 16.2225 13.8941 15.615 14.2616L10.7925 17.1941C10.3875 17.4416 9.94501 17.5691 9.50251 17.5691ZM11.3625 7.60162C10.9275 7.60162 10.4925 7.7216 10.0875 7.9616L7.7175 9.40159C6.63 10.0616 6.24001 11.4191 6.79501 12.5591L8.33251 15.6941C8.50501 16.0391 8.79001 16.2866 9.14251 16.3916C9.49501 16.4966 9.87001 16.4366 10.1925 16.2416L15.015 13.3091C15.345 13.1066 15.5625 12.7991 15.6375 12.4391C15.705 12.0791 15.6225 11.7116 15.39 11.4041L13.3125 8.59913C12.84 7.94663 12.1125 7.60162 11.3625 7.60162Z"
      fill="#292D32"
    />
    <path
      d="M6.69757 9.93353C6.65257 9.93353 6.60757 9.92601 6.56257 9.91851C6.42007 9.88101 6.29257 9.79104 6.21757 9.66354L4.65758 7.09855C4.49258 6.83605 4.58258 6.49103 4.84508 6.32603L8.68507 3.98603C8.94757 3.82853 9.30007 3.91103 9.45757 4.17353L11.0176 6.73852C11.0926 6.86602 11.1226 7.01602 11.0851 7.16602C11.0476 7.30852 10.9576 7.43604 10.8301 7.51104L6.99008 9.85104C6.90008 9.91104 6.79507 9.93353 6.69757 9.93353ZM5.91007 7.00105L6.88507 8.60601L9.76507 6.85104L8.79007 5.24603L5.91007 7.00105Z"
      fill="#292D32"
    />
    <path
      d="M12.9076 15.814C12.7201 15.814 12.5326 15.7165 12.4276 15.544L11.19 13.5115C11.025 13.249 11.115 12.904 11.3775 12.739C11.64 12.5815 11.9925 12.664 12.15 12.9265L13.3876 14.959C13.5526 15.2215 13.4626 15.5665 13.2001 15.7315C13.1101 15.784 13.0051 15.814 12.9076 15.814Z"
      fill="#292D32"
    />
    <path
      d="M10.9875 16.9839C10.8 16.9839 10.6125 16.8864 10.5075 16.7139L9.27 14.6814C9.105 14.4189 9.195 14.0739 9.4575 13.9089C9.72 13.7514 10.0725 13.8339 10.23 14.0964L11.4675 16.1289C11.6325 16.3914 11.5425 16.7364 11.28 16.9014C11.19 16.9539 11.085 16.9839 10.9875 16.9839Z"
      fill="#292D32"
    />
    <path
      d="M14.8276 14.6441C14.6401 14.6441 14.4526 14.5465 14.3476 14.374L13.1101 12.3416C12.9451 12.0791 13.0351 11.734 13.2976 11.569C13.5601 11.4115 13.9126 11.494 14.0701 11.7565L15.3076 13.7891C15.4726 14.0516 15.3826 14.3965 15.1201 14.5615C15.0301 14.614 14.9326 14.6441 14.8276 14.6441Z"
      fill="#292D32"
    />
  </svg>
);
export const DowloadIcon = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="18"
    viewBox="0 0 16 18"
    fill="none"
  >
    <path
      d="M6 13.0729C5.93333 13.0729 5.87333 13.0587 5.80667 13.0304C5.62 12.9525 5.5 12.7541 5.5 12.5416V8.29163C5.5 8.00121 5.72667 7.76038 6 7.76038C6.27333 7.76038 6.5 8.00121 6.5 8.29163V11.2595L6.98 10.7495C7.17333 10.5441 7.49333 10.5441 7.68667 10.7495C7.88 10.955 7.88 11.295 7.68667 11.5004L6.35333 12.917C6.26 13.0162 6.12667 13.0729 6 13.0729Z"
      fill="white"
    />
    <path
      d="M6.00012 13.073C5.87346 13.073 5.74679 13.0234 5.64679 12.9171L4.31346 11.5005C4.12012 11.2951 4.12012 10.9551 4.31346 10.7496C4.50679 10.5442 4.82679 10.5442 5.02012 10.7496L6.35346 12.1663C6.54679 12.3717 6.54679 12.7117 6.35346 12.9171C6.25346 13.0234 6.12679 13.073 6.00012 13.073Z"
      fill="white"
    />
    <path
      d="M10.0002 16.6145H6.00016C2.38016 16.6145 0.833496 14.9712 0.833496 11.125V6.87496C0.833496 3.02871 2.38016 1.38538 6.00016 1.38538H9.3335C9.60683 1.38538 9.8335 1.62621 9.8335 1.91663C9.8335 2.20704 9.60683 2.44788 9.3335 2.44788H6.00016C2.92683 2.44788 1.8335 3.60954 1.8335 6.87496V11.125C1.8335 14.3904 2.92683 15.552 6.00016 15.552H10.0002C13.0735 15.552 14.1668 14.3904 14.1668 11.125V7.58329C14.1668 7.29288 14.3935 7.05204 14.6668 7.05204C14.9402 7.05204 15.1668 7.29288 15.1668 7.58329V11.125C15.1668 14.9712 13.6202 16.6145 10.0002 16.6145Z"
      fill="white"
    />
    <path
      d="M14.6668 8.1146H12.0002C9.72016 8.1146 8.8335 7.17251 8.8335 4.75001V1.91668C8.8335 1.70418 8.9535 1.50585 9.14016 1.42793C9.32683 1.34293 9.54016 1.39251 9.68683 1.54126L15.0202 7.20793C15.1602 7.35668 15.2068 7.59043 15.1268 7.78876C15.0468 7.9871 14.8668 8.1146 14.6668 8.1146ZM9.8335 3.19876V4.75001C9.8335 6.57751 10.2802 7.0521 12.0002 7.0521H13.4602L9.8335 3.19876Z"
      fill="white"
    />
  </svg>
);
