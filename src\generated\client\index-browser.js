
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.9.0
 * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
 */
Prisma.prismaVersion = {
  client: "6.9.0",
  engine: "81e4af48011447c3cc503a190e86995b66d2a28e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.Academic_yearScalarFieldEnum = {
  id: 'id',
  created_at: 'created_at',
  end_date: 'end_date',
  start_date: 'start_date',
  status: 'status',
  updated_at: 'updated_at',
  year_label: 'year_label'
};

exports.Prisma.BookScalarFieldEnum = {
  id: 'id',
  created_at: 'created_at',
  name: 'name',
  status: 'status',
  updated_at: 'updated_at',
  subject_id: 'subject_id'
};

exports.Prisma.ChapterScalarFieldEnum = {
  id: 'id',
  created_at: 'created_at',
  name: 'name',
  status: 'status',
  updated_at: 'updated_at',
  book_id: 'book_id'
};

exports.Prisma.GradeScalarFieldEnum = {
  id: 'id',
  created_at: 'created_at',
  name: 'name',
  status: 'status',
  updated_at: 'updated_at'
};

exports.Prisma.LessonScalarFieldEnum = {
  id: 'id',
  created_at: 'created_at',
  name: 'name',
  status: 'status',
  updated_at: 'updated_at',
  chapter_id: 'chapter_id'
};

exports.Prisma.Refresh_tokenScalarFieldEnum = {
  id: 'id',
  expires_at: 'expires_at',
  is_revoked: 'is_revoked',
  issued_at: 'issued_at',
  token: 'token',
  user_id: 'user_id'
};

exports.Prisma.SubjectScalarFieldEnum = {
  id: 'id',
  created_at: 'created_at',
  name: 'name',
  status: 'status',
  updated_at: 'updated_at',
  grade_id: 'grade_id'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  full_name: 'full_name',
  password: 'password',
  role: 'role',
  username: 'username'
};

exports.Prisma.Work_spaceScalarFieldEnum = {
  id: 'id',
  name: 'name',
  academic_year_id: 'academic_year_id',
  account_id: 'account_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.Book_typeScalarFieldEnum = {
  id: 'id',
  created_at: 'created_at',
  description: 'description',
  icon: 'icon',
  name: 'name',
  status: 'status',
  updated_at: 'updated_at',
  token_cost_per_query: 'token_cost_per_query',
  priority: 'priority'
};

exports.Prisma.FormScalarFieldEnum = {
  id: 'id',
  form_definition: 'form_definition',
  created_at: 'created_at',
  form_description: 'form_description',
  form_name: 'form_name',
  updated_at: 'updated_at',
  form_status: 'form_status'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.academic_yearOrderByRelevanceFieldEnum = {
  year_label: 'year_label'
};

exports.Prisma.bookOrderByRelevanceFieldEnum = {
  created_at: 'created_at',
  name: 'name',
  updated_at: 'updated_at'
};

exports.Prisma.chapterOrderByRelevanceFieldEnum = {
  created_at: 'created_at',
  name: 'name',
  updated_at: 'updated_at'
};

exports.Prisma.gradeOrderByRelevanceFieldEnum = {
  created_at: 'created_at',
  name: 'name',
  updated_at: 'updated_at'
};

exports.Prisma.lessonOrderByRelevanceFieldEnum = {
  created_at: 'created_at',
  name: 'name',
  updated_at: 'updated_at'
};

exports.Prisma.refresh_tokenOrderByRelevanceFieldEnum = {
  token: 'token'
};

exports.Prisma.subjectOrderByRelevanceFieldEnum = {
  created_at: 'created_at',
  name: 'name',
  updated_at: 'updated_at'
};

exports.Prisma.userOrderByRelevanceFieldEnum = {
  email: 'email',
  full_name: 'full_name',
  password: 'password',
  username: 'username'
};

exports.Prisma.work_spaceOrderByRelevanceFieldEnum = {
  name: 'name',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.book_typeOrderByRelevanceFieldEnum = {
  created_at: 'created_at',
  description: 'description',
  icon: 'icon',
  name: 'name',
  updated_at: 'updated_at'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.formOrderByRelevanceFieldEnum = {
  created_at: 'created_at',
  form_description: 'form_description',
  form_name: 'form_name',
  updated_at: 'updated_at'
};
exports.academic_year_status = exports.$Enums.academic_year_status = {
  INACTIVE: 'INACTIVE',
  ACTIVE: 'ACTIVE',
  UPCOMING: 'UPCOMING'
};

exports.book_status = exports.$Enums.book_status = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.chapter_status = exports.$Enums.chapter_status = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.grade_status = exports.$Enums.grade_status = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.lesson_status = exports.$Enums.lesson_status = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.subject_status = exports.$Enums.subject_status = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.user_role = exports.$Enums.user_role = {
  TEACHER: 'TEACHER',
  ADMIN: 'ADMIN',
  STAFF: 'STAFF'
};

exports.book_type_status = exports.$Enums.book_type_status = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE'
};

exports.Prisma.ModelName = {
  academic_year: 'academic_year',
  book: 'book',
  chapter: 'chapter',
  grade: 'grade',
  lesson: 'lesson',
  refresh_token: 'refresh_token',
  subject: 'subject',
  user: 'user',
  work_space: 'work_space',
  book_type: 'book_type',
  form: 'form'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
