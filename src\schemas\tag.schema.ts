import { z } from "zod";

export const tagSchema = z.object({
  name: z
    .string()
    .transform((val) => val.trim())
    .refine((val) => val.length > 0, {
      message: "<PERSON><PERSON><PERSON> chức năng không được để trống",
    }),
  description: z
    .string({ required_error: "<PERSON><PERSON> tả không được để trống" })
    .transform((val) => val.trim())
    .refine((val) => val.length > 0, {
      message: "<PERSON>ô tả không được để trống",
    }),
});

export type TagData = z.infer<typeof tagSchema>;
