import {
  createMuta<PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  updateMutation<PERSON>ook,
  patchMutation<PERSON>ook
} from "@/hooks/react-query";
import { API_ENDPOINTS } from "@/constants/apiEndpoints";

export const useBookTypesService = createQueryHook("bookTypes", API_ENDPOINTS.BOOK_TYPES);

export const useCreateBookTypeService = createMutationHook(
  "bookTypes",
  API_ENDPOINTS.BOOK_TYPES
);

export const useUpdateBookTypeService = updateMutationHook(
  "bookTypes",
  API_ENDPOINTS.BOOK_TYPES
);

export const useUpdateBookTypeStatus = patchMutationHook("bookTypes", API_ENDPOINTS.BOOK_TYPES);
