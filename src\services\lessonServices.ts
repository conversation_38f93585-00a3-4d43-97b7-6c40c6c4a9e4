import {
  createMulti<PERSON><PERSON>y<PERSON><PERSON>,
  createM<PERSON><PERSON><PERSON><PERSON>,
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  createQ<PERSON>yWithPathParamHook,
  updateMutationHook,
} from "@/hooks/react-query";
import { API_ENDPOINTS } from "@/constants/apiEndpoints";

export const useLessonsService = createQueryHook(
  "lessons",
  API_ENDPOINTS.LESSONS
);
export const useLessonByIdService = createQueryWithPathParamHook(
  "lessonById",
  API_ENDPOINTS.LESSONS
);

export const useCreateLessonService = createMutationHook(
  "lessons",
  API_ENDPOINTS.LESSONS
);
export const useLessonsByChapterService = createQueryWithPathParamHook(
  "lessonsByChapter",
  API_ENDPOINTS.LESSONS_BY_CHAPTER
);
export const useUpdateLessonService = updateMutationHook(
  "lessonsByChapter",
  API_ENDPOINTS.LESSONS
);

export const useLessonsByChaptersService = createMultiQueryHook(
  "lessonsByChapter",
  (chapterId) => `${API_ENDPOINTS.LESSONS_BY_CHAPTER}/${chapterId}`
);
