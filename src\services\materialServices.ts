import {
  createMutationHook,
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  createQueryWithPathParamHook,
} from "@/hooks/react-query";
import { API_ENDPOINTS } from "@/constants/apiEndpoints";

export const useMaterialervice = createQueryHook(
  "materials",
  API_ENDPOINTS.ACADEMIC_RESOURCE
);

import { useQuery } from "@tanstack/react-query";
import api from "@/config/axios";

// Custom hook with dynamic queryKey for refetching when tagId changes
export const useMaterialSearchService = (tagIds?: string) => {
  return useQuery({
    queryKey: ["materials-search", tagIds], // Include tagId in queryKey
    queryFn: async () => {
      if (!tagIds) return null;
      const response = await api.get(API_ENDPOINTS.ACADEMIC_RESOURCE_SEARCH, {
        params: { tagIds }
      });
      return response.data;
    },
    enabled: !!tagIds, // Only run when tagId exists
  });
};

export const useCreateMaterialService = createMutationHook(
  "materials",
  API_ENDPOINTS.ACADEMIC_RESOURCE_UPLOAD
);
// export const useUpdateBookStatus = patchMutationHook("books", API_ENDPOINTS.BOOKS);
